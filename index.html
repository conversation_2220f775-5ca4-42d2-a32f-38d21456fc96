<!DOCTYPE html>
   <html lang="en">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">

      <!--=============== FAVICON ===============-->
      <link rel="shortcut icon" href="assets/img/favicon.png" type="image/x-icon">

      <!--=============== REMIXICONS ===============-->
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.2.0/remixicon.css">

      <!--=============== CSS ===============-->
      <link rel="stylesheet" href="assets/css/styles.css">

      <title>Responsive portfolio website - Bedimcode</title>
   </head>
   <body>
      <!--==================== NAV ====================-->
      <nav class="nav">
         <a href="#home" class="nav__link">Home</a>
         <a href="#projects" class="nav__link">Projects</a>
         <a href="#services" class="nav__link">Services</a>
         <a href="#experience" class="nav__link">Experience</a>
         <a href="#contact" class="nav__link">Contact</a>
      </nav>

      <!--==================== MAIN ====================-->
      <main class="main">
         <!--==================== HOME ====================-->
         <section class="home section" id="home">
            <div class="home__container container grid">
               <!--===== PERFIL =====-->
               <div class="perfil">
                  <div class="perfil__name">
                     Xian <br> Gallers
                  </div>
                  <div class="perfil__buttons">
                     <a href="#projects" class="button">Projects</a>
                     <a href="#services" class="button">Services</a>
                  </div>
               </div>

               <!--===== INFO =====-->
               <div class="info">
                  <div class="info__name">
                     Xian Gallers
                  </div>
                  <div class="info__description">
                     Passionate about creating and designing 
                     websites with the best engaging interfaces.
                  </div>
                  <a href="assets/pdf/Xian-Cv.pdf" class="button" download>Download CV</a>
               </div>

               <!--===== ABOUT =====-->
               <div class="about">
                  <div class="about__name">
                     Xian Gallers - <b>Web Designer & Developer</b>
                  </div>
                  <div class="about__description">
                     Located in Peru, I have several years of 
                     experience in web development and design, 
                     carrying out several successful projects.
                  </div>
                  <div class="about__links">
                     <a href="https://www.linkedin.com/" target="_blank" rel="noopener" class="about__link" title="LinkedIn">
                        <i class="ri-linkedin-box-fill"></i>
                     </a>
                     <a href="https://github.com/" target="_blank" rel="noopener" class="about__link" title="GitHub">
                        <i class="ri-github-fill"></i>
                     </a>
                     <a href="https://www.behance.net/" target="_blank" rel="noopener" class="about__link" title="Behance">
                        <i class="ri-behance-fill"></i>
                     </a>
                  </div>
                  <p class="about__note">
                     He doesn't write messages on social networks, 
                     send me an email and I'll answer you.
                  </p>
                  <a href="#contact" class="button">Contact Me</a>
               </div>

               <!--===== SKILLS =====-->
               <div class="skills">
                  <h3 class="skills__title">Skills</h3>
                  <p class="skills__description">
                     Visit the projects section to see the 
                     work done with these web technologies.
                  </p>
               </div>
            </div>
         </section>

         <!--==================== PROJECTS ====================-->
         <section class="projects section" id="projects">
            <h2 class="section__title">RECENT PROJECTS</h2>
            <div class="projects__container container grid">
               <article class="projects__card">
                  <img src="assets/img/projects-1.jpg" alt="Coffee Website" class="projects__img">
                  <h3 class="projects__name">Coffee Website</h3>
                  <p class="projects__description">
                     Short description of the project that 
                     was carried out in this portfolio.
                  </p>
                  <a href="#" class="projects__button">
                     <i class="ri-arrow-right-line"></i>
                     <span>Visit Project</span>
                  </a>
               </article>
               <article class="projects__card">
                  <img src="assets/img/projects-2.jpg" alt="Food Product Design" class="projects__img">
                  <h3 class="projects__name">Food Product Design</h3>
                  <p class="projects__description">
                     Short description of the project that 
                     was carried out in this portfolio.
                  </p>
                  <a href="#" class="projects__button">
                     <i class="ri-arrow-right-line"></i>
                     <span>Visit Project</span>
                  </a>
               </article>
               <article class="projects__card">
                  <img src="assets/img/projects-3.jpg" alt="Restaurant Landing Page" class="projects__img">
                  <h3 class="projects__name">Restaurant Landing Page</h3>
                  <p class="projects__description">
                     Short description of the project that 
                     was carried out in this portfolio.
                  </p>
                  <a href="#" class="projects__button">
                     <i class="ri-arrow-right-line"></i>
                     <span>Visit Project</span>
                  </a>
               </article>
               <article class="projects__card">
                  <img src="assets/img/projects-4.jpg" alt="Barbershop Website" class="projects__img">
                  <h3 class="projects__name">Barbershop Website</h3>
                  <p class="projects__description">
                     Short description of the project that 
                     was carried out in this portfolio.
                  </p>
                  <a href="#" class="projects__button">
                     <i class="ri-arrow-right-line"></i>
                     <span>Visit Project</span>
                  </a>
               </article>
               <article class="projects__card">
                  <img src="assets/img/projects-5.jpg" alt="Construction Landing Page" class="projects__img">
                  <h3 class="projects__name">Construction Landing Page</h3>
                  <p class="projects__description">
                     Short description of the project that 
                     was carried out in this portfolio.
                  </p>
                  <a href="#" class="projects__button">
                     <i class="ri-arrow-right-line"></i>
                     <span>Visit Project</span>
                  </a>
               </article>
            </div>
         </section>

         <!--==================== SERVICES ====================-->
         <section class="services section" id="services">
            <h2 class="section__title">SERVICES I OFFER</h2>
            <div class="services__container container grid">
               <article class="services__card">
                  <i class="ri-code-line services__icon"></i>
                  <h3 class="services__name">Web Development</h3>
                  <p class="services__description">
                     Short description of the service offered 
                     to users to obtain a personalized project.
                  </p>
               </article>
               <article class="services__card">
                  <i class="ri-palette-line services__icon"></i>
                  <h3 class="services__name">Web Design</h3>
                  <p class="services__description">
                     Short description of the service offered 
                     to users to obtain a personalized project.
                  </p>
               </article>
               <article class="services__card">
                  <i class="ri-search-line services__icon"></i>
                  <h3 class="services__name">Seo (Web Pages)</h3>
                  <p class="services__description">
                     Short description of the service offered 
                     to users to obtain a personalized project.
                  </p>
               </article>
            </div>
         </section>         <!--==================== EXPERIENCE ====================-->
         <section class="experience section" id="experience">
            <h2 class="section__title">EXPERIENCE OR WORK</h2>
            <div class="experience__container container grid">
               <article class="experience__card">
                  <div class="experience__header">
                     <h3 class="experience__company">Adobe</h3>
                     <span class="experience__profession">Website Development</span>
                     <span class="experience__date">08/24 - Present</span>
                  </div>
                  <p class="experience__description">
                     Short description of the work 
                     you do or did in the company.
                  </p>
               </article>
               <article class="experience__card">
                  <div class="experience__header">
                     <h3 class="experience__company">Google</h3>
                     <span class="experience__profession">Web Product Designer</span>
                     <span class="experience__date">02/22 - 01/23</span>
                  </div>
                  <p class="experience__description">
                     Short description of the work 
                     you do or did in the company.
                  </p>
               </article>
               <article class="experience__card">
                  <div class="experience__header">
                     <h3 class="experience__company">Figma</h3>
                     <span class="experience__profession">Web Designer</span>
                     <span class="experience__date">05/23 - 11/23</span>
                  </div>
                  <p class="experience__description">
                     Short description of the work 
                     you do or did in the company.
                  </p>
               </article>
            </div>
         </section>

         <!--==================== CONTACT ====================-->
         <section class="contact section" id="contact">
            <h2 class="section__title">CONTACT ME</h2>
            <div class="contact__container container grid">
               <form action="" class="contact__form" id="form">
                  <div class="contact__inputs">
                     <div class="contact__content">
                        <label for="name" class="contact__label">Names</label>
                        <input type="text" class="contact__input" id="name" name="user_name" placeholder="Names" required>
                     </div>
                     <div class="contact__content">
                        <label for="email" class="contact__label">Email</label>
                        <input type="email" class="contact__input" id="email" name="user_email" placeholder="Email" required>
                     </div>
                     <div class="contact__content contact__area">
                        <label for="message" class="contact__label">Message</label>
                        <textarea name="user_message" id="message" class="contact__input" placeholder="Message" required></textarea>
                     </div>
                  </div>
                  <button type="submit" class="contact__button">
                     Send Message
                     <i class="ri-send-plane-line"></i>
                  </button>
               </form>
            </div>
         </section>
      </main>

      <!--==================== FOOTER ====================-->
      <footer class="footer">
         <div class="footer__container container">
            <div class="footer__logo">Xian</div>
            <div class="footer__links">
               <a href="#home" class="footer__link">Home</a>
               <a href="#projects" class="footer__link">Projects</a>
               <a href="#services" class="footer__link">Services</a>
            </div>
            <div class="footer__social">
               <a href="https://www.facebook.com/" target="_blank" rel="noopener" class="footer__social-link" title="Facebook">
                  <i class="ri-facebook-fill"></i>
               </a>
               <a href="https://www.instagram.com/" target="_blank" rel="noopener" class="footer__social-link" title="Instagram">
                  <i class="ri-instagram-fill"></i>
               </a>
               <a href="https://twitter.com/" target="_blank" rel="noopener" class="footer__social-link" title="Twitter">
                  <i class="ri-twitter-fill"></i>
               </a>
            </div>
            <span class="footer__copy">&#169; All Rights Reserved By Bedimcode</span>
         </div>
      </footer>

      <!--========== SCROLL UP ==========-->
      <a href="#home" class="scrollup" id="scroll-up" title="Scroll to top">
         <i class="ri-arrow-up-line"></i>
      </a>


      <!--=============== SCROLLREVEAL ===============-->
      <script src="assets/js/scrollreveal.min.js"></script>

      <!--=============== EMAIL JS ===============-->
      <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>

      <!--=============== MAIN JS ===============-->
      <script src="assets/js/main.js"></script>
   </body>
</html>
/*=============== GOOGLE FONTS ===============*/
@import url("https://fonts.googleapis.com/css2?family=Grechen+Fuemen&family=Montserrat+Alternates:wght@400;500;600&display=swap");

/*=============== VARIABLES CSS ===============*/
:root {
  /*========== Colors ==========*/
  /*Color mode HSL(hue, saturation, lightness)*/
  /* 
         Change favorite color:

         Default (Red): hsl(358, 80%, 49%)
         Purple: hsl(265, 80%, 49%) - Blue: hsl(220, 80%, 49%)
         Pink: hsl(330, 80%, 49%) - Green: hsl(162, 70%, 40%)
         Orange: hsl(14, 80%, 49%)

         For more colors visit: https://htmlcolorcodes.com/
         -> Choose any color 
         -> Copy the color mode (HSL)
  */
  --hue: 358;
  --first-color: hsl(var(--hue), 80%, 49%);
  --first-color-alt: hsl(var(--hue), 76%, 45%);
  --white-color: hsl(0, 0%, 98%);
  --black-color: hsl(0, 0%, 1%);
  --text-color: hsl(var(--hue), 2%, 66%);
  --body-color: hsl(var(--hue), 100%, 1%);
  --container-color: hsl(var(--hue), 2%, 10%);
  --conic-gradient: conic-gradient(from 150deg at 50% 45%,
                  hsl(var(--hue), 80%, 20%) 0deg,
                  hsl(var(--hue), 80%, 48%) 140deg,
                  hsl(var(--hue), 80%, 20%) 360deg);

  /*========== Font and typography ==========*/
  /*.5rem = 8px | 1rem = 16px ...*/
  --body-font: "Montserrat Alternates", sans-serif;
  --second-font: "Grechen Fuemen", cursive;
  --biggest-font-size: 2.5rem;
  --h1-font-size: 1.5rem;
  --h2-font-size: 1.25rem;
  --h3-font-size: 1rem;
  --normal-font-size: .938rem;
  --small-font-size: .813rem;
  --smaller-font-size: .75rem;

  /*========== Font weight ==========*/
  --font-regular: 400;
  --font-medium: 500;
  --font-semi-bold: 600;

  /*========== z index ==========*/
  --z-tooltip: 10;
  --z-fixed: 100;
}

/*========== Responsive typography ==========*/
@media screen and (min-width: 1168px) {
  :root {
    --biggest-font-size: 3.5rem;
    --h1-font-size: 2.25rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.25rem;
    --normal-font-size: 1rem;
    --small-font-size: .875rem;
    --smaller-font-size: .813rem;
  }
}

/*=============== BASE ===============*/
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body,
input,
textarea,
button {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
}

body {
  background-color: var(--body-color);
  color: var(--white-color);
}

input,
button,
textarea {
  border: none;
  outline: none;
}

h1, h2, h3, h4 {
  color: var(--white-color);
  font-weight: var(--font-semi-bold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
}

/*=============== REUSABLE CSS CLASSES ===============*/
.container {
  max-width: 1168px;
  margin-inline: 1.5rem;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.section {
  padding-block: 2rem 3rem;
}

.section__title {
  font-size: var(--h2-font-size);
  text-align: center;
  margin-bottom: 2rem;
}

.main {
  overflow: hidden;
}

/*=============== NAV ===============*/


/* Active link */


/*=============== HOME ===============*/
/*===== PERFIL =====*/


/*===== INFO =====*/


/*===== ABOUT =====*/


/*===== SKILLS =====*/


/*=============== BUTTON ===============*/


/*=============== PROJECTS ===============*/


/*=============== SERVICES ===============*/


/*=============== EXPERIENCE ===============*/


/*=============== CONTACT ===============*/


/*=============== FOOTER ===============*/


/*=============== SCROLL BAR ===============*/


/*=============== SCROLL UP ===============*/


/* Show Scroll Up */


/*=============== BREAKPOINTS ===============*/
/* For small devices */


/* For medium devices */


/* For large devices */
